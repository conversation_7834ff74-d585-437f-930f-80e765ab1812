import os
import sys

print("开始身份证OCR识别...")

# 修复PIL版本兼容性问题
try:
    from PIL import Image
    if not hasattr(Image, 'ANTIALIAS'):
        Image.ANTIALIAS = Image.LANCZOS
    print("PIL兼容性修复完成")
except ImportError:
    pass

try:
    import ddddocr
    print("ddddocr库导入成功")
except ImportError as e:
    print(f"ddddocr库导入失败: {e}")
    print("请先安装ddddocr库: pip install ddddocr")
    sys.exit(1)

try:
    # 指定要识别的身份证图片路径
    image_path = r'id_card_sorted\正面\20250730_155720_孟祥玲安徽村1977.JPG'
    print(f"目标图片路径: {image_path}")

    # 检查文件是否存在
    if os.path.exists(image_path):
        print("图片文件存在，开始读取...")
        # 读取身份证图片
        with open(image_path, 'rb') as f:
            img_bytes = f.read()

        print(f"图片文件大小: {len(img_bytes)} 字节")

        # 尝试不同的OCR模式
        print("\n=== 尝试通用OCR模式 ===")
        try:
            ocr_general = ddddocr.DdddOcr(show_ad=False)
            result_general = ocr_general.classification(img_bytes)
            print(f"通用OCR识别结果: {result_general}")
        except Exception as e:
            print(f"通用OCR识别失败: {e}")

        print("\n=== 尝试验证码OCR模式 ===")
        try:
            ocr_captcha = ddddocr.DdddOcr(old=True, show_ad=False)
            result_captcha = ocr_captcha.classification(img_bytes)
            print(f"验证码OCR识别结果: {result_captcha}")
        except Exception as e:
            print(f"验证码OCR识别失败: {e}")

        print("\n=== 尝试使用图像预处理 ===")
        try:
            from PIL import Image, ImageEnhance, ImageFilter
            import io

            # 打开图像
            image = Image.open(io.BytesIO(img_bytes))
            print(f"原始图像尺寸: {image.size}")

            # 转换为灰度图
            if image.mode != 'L':
                image = image.convert('L')

            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)

            # 锐化
            image = image.filter(ImageFilter.SHARPEN)

            # 保存处理后的图像到字节流
            processed_bytes = io.BytesIO()
            image.save(processed_bytes, format='PNG')
            processed_img_bytes = processed_bytes.getvalue()

            # 使用处理后的图像进行OCR
            ocr_processed = ddddocr.DdddOcr(show_ad=False)
            result_processed = ocr_processed.classification(processed_img_bytes)
            print(f"预处理后OCR识别结果: {result_processed}")

        except Exception as e:
            print(f"图像预处理OCR识别失败: {e}")
    else:
        print(f"文件不存在: {image_path}")
        # 列出当前目录下的文件
        print("当前目录内容:")
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith(('.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG')):
                    print(os.path.join(root, file))

except Exception as e:
    print(f"发生错误: {e}")
    import traceback
    traceback.print_exc()