import os
import sys
import cv2
import numpy as np
from pathlib import Path

print("开始身份证OCR识别...")

try:
    from cnocr import CnOcr
    print("CnOcr库导入成功")
except ImportError as e:
    print(f"CnOcr库导入失败: {e}")
    print("请先安装CnOcr库: pip install cnocr")
    sys.exit(1)

def imread_with_unicode(path):
    """使用 imdecode 解决中文路径读取问题"""
    try:
        with open(path, 'rb') as f:
            img_data = f.read()
        return cv2.imdecode(np.frombuffer(img_data, np.uint8), cv2.IMREAD_COLOR)
    except Exception as e:
        print(f"使用imdecode读取图片时出错: {e}")
        return None

def preprocess_image(img_np):
    """对图像进行预处理，仅转换为灰度图"""
    if img_np is None:
        return None
    # 仅转换为灰度图，保留所有亮度信息
    gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)
    return gray

def identify_id_card_side(ocr_results):
    """
    根据OCR结果识别身份证正反面
    :param ocr_results: OCR识别结果列表
    :return: 'front' (正面), 'back' (反面), 'unknown' (未识别)
    """
    if not ocr_results:
        return 'unknown'

    # 提取所有识别到的文字
    all_text = ' '.join([result.get('text', '') for result in ocr_results])

    # 身份证正面关键词
    front_keywords = [
        '中华人民共和国', '居民身份证', '姓名', '性别', '民族',
        '出生', '住址', '公民身份号码', '身份证号码'
    ]

    # 身份证反面关键词
    back_keywords = [
        '签发机关', '有效期限', '公安局', '派出所',
        '有效期', '长期', '签发日期'
    ]

    front_score = 0
    back_score = 0

    # 检查正面关键词
    for keyword in front_keywords:
        if keyword in all_text:
            front_score += 1

    # 检查反面关键词
    for keyword in back_keywords:
        if keyword in all_text:
            back_score += 1

    # 判断正反面
    if front_score > back_score and front_score >= 2:
        return 'front'
    elif back_score > front_score and back_score >= 1:
        return 'back'
    else:
        return 'unknown'

try:
    # 初始化CnOcr引擎，并使用中文模型
    print("正在初始化CnOcr引擎...")
    ocr = CnOcr(det_model_name='ch_PP-OCRv3_det', rec_model_name='ch_PP-OCRv3')
    print("CnOcr引擎初始化成功")

    # 指定要识别的身份证图片路径
    image_path = r'id_card_sorted\正面\20250730_155720_孟祥玲安徽村1977.JPG'
    print(f"目标图片路径: {image_path}")

    # 检查文件是否存在
    if os.path.exists(image_path):
        print("图片文件存在，开始读取...")

        # 使用支持中文路径的方式读取图片
        img_np = imread_with_unicode(image_path)
        if img_np is None:
            print(f"错误：无法读取图片 {image_path}")
            sys.exit(1)

        print(f"图片尺寸: {img_np.shape}")

        # 对图像进行预处理
        processed_img_np = preprocess_image(img_np)

        # 使用CnOcr进行OCR识别
        print("开始OCR识别...")
        result = ocr.ocr(processed_img_np)

        if result:
            print(f"识别到 {len(result)} 行文字")

            # 结果后处理
            filtered_results = []
            confidence_threshold = 0.5  # 置信度阈值
            min_text_length = 1         # 最小文本长度

            for line in result:
                confidence = line.get('score', 0.0)
                text = "".join(line['text']).strip()

                # 应用过滤规则
                if confidence >= confidence_threshold and len(text) >= min_text_length:
                    filtered_results.append({
                        "text": text,
                        "confidence": float(confidence),
                        "position": line['position']
                    })

            if filtered_results:
                print("\n=== 识别结果 ===")
                for item in filtered_results:
                    print(f"文字: {item['text']} (置信度: {item['confidence']:.4f})")

                # 识别身份证正反面
                side = identify_id_card_side(filtered_results)
                side_names = {'front': '正面', 'back': '反面', 'unknown': '未识别'}
                print(f"\n身份证类型: {side_names[side]}")

                # 提取关键信息（如果是正面）
                if side == 'front':
                    print("\n=== 提取的关键信息 ===")
                    all_text = ' '.join([item['text'] for item in filtered_results])

                    # 尝试提取姓名、身份证号等信息
                    for item in filtered_results:
                        text = item['text']
                        if '姓名' in text:
                            print(f"可能包含姓名信息: {text}")
                        elif len(text) == 18 and text.isdigit():
                            print(f"可能的身份证号: {text}")
                        elif '性别' in text:
                            print(f"可能包含性别信息: {text}")
                        elif '民族' in text:
                            print(f"可能包含民族信息: {text}")
                        elif '出生' in text:
                            print(f"可能包含出生信息: {text}")
                        elif '住址' in text or '地址' in text:
                            print(f"可能包含地址信息: {text}")
            else:
                print("未识别到有效文字")
        else:
            print("OCR识别失败，未识别到任何文字")
    else:
        print(f"文件不存在: {image_path}")
        # 列出当前目录下的图片文件
        print("当前目录下的图片文件:")
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')):
                    print(os.path.join(root, file))

except Exception as e:
    print(f"发生错误: {e}")
    import traceback
    traceback.print_exc()